@use 'shared' as *;

.recommendation-block {
  display: flex;
  flex-direction: column;
  grid-gap: 34px 16px;
  margin-bottom: 4px;
  margin-top: 36px;

  &-external {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    grid-gap: 30px 20px;
  }

  .full-row {
    grid-column: span 3 / span 6;

    @include media-breakpoint-down(md) {
      grid-column: span 1;
    }
  }
}
