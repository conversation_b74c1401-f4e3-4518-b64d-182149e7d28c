@use 'shared' as *;

:host {
  display: block;
  margin-bottom: 20px;
  font-family: var(--kui-font-primary);

  &.article-card {
    $fontSizes: 20, 22, 24, 28;

    // Difference should be same between font-size and line-height.
    // For example: 20:26, 22:28, 24:30, 28:34;
    $difference: 6px;

    @each $fontSize in $fontSizes {
      .fs-#{$fontSize} {
        .mobile-box .title,
        .article-card-title {
          line-height: ($fontSize + $difference);
        }
      }
    }
  }

  .is-manual {
    .icon-mandiner-play,
    .article-card-video::before {
      display: none;
    }
  }

  .article-card-tags[data-tag-hide-labels='true'] {
    .article-card-tags-item {
      &:hover {
        span {
          display: block;
          visibility: initial;
        }
      }

      &:not(:hover) .icon {
        margin-right: 0;
      }

      span {
        display: none;
        visibility: hidden;
      }
    }
  }

  .article-card-tags-item span {
    line-height: 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .article-card-figure-image {
    width: 100%;
    object-fit: cover;
  }

  .icon-mplus {
    background: var(--kui-black);
    color: var(--kui-orange-600);
    font-weight: 800;
    padding: 5px 2px;
    font-size: 14px;
    line-height: 14px;
    display: inline-block;
    flex-grow: 0;
    flex-shrink: 0;
    margin-right: 5px;
  }

  .flex-container {
    display: flex;
    gap: 5px;
  }

  .caption {
    .title,
    .lead,
    .article-card-lead,
    .article-card-title {
      text-transform: uppercase;
    }
  }

  .highlight {
    .article-card-title {
      font-size: 28px;
    }

    .article-card-title.big {
      font-size: 36px;
    }

    .article-card-lead {
      font-size: 20px;
    }

    .article-card-lead.big {
      font-size: 22px;
    }

    .title.big {
      font-size: 36px;
    }

    .lead.big {
      font-size: 22px;
    }
  }

  .italic {
    .title,
    .lead,
    .article-card-lead,
    .article-card-title {
      font-style: italic;
    }
  }

  .article-card {
    &-tags {
      display: flex;

      &-top {
        margin-bottom: 5px;
      }

      &-item {
        height: 24px;
        padding: 0 7px;
        display: flex;
        align-items: center;
        border: 1px solid var(--kui-orange-600);
        text-transform: uppercase;
        font-size: 12px;
        font-weight: 700;
        line-height: 15px;
        margin-right: 5px;

        &.adult-tag {
          background: var(--kui-red-900);
          color: var(--kui-white);
          border: none;
        }

        .icon {
          height: 12px;
          width: 12px;
          margin-right: 5px;
        }
      }
    }

    &-title {
      font-family: var(--kui-font-secondary);
      transition-duration: 0.3s;
      @include media-breakpoint-up(lg) {
        &:hover {
          color: var(--kui-orange-600) !important;
          transition-duration: 0.3s;
        }
      }
    }

    &-link {
      color: var(--kui-black);
    }

    &-category {
      color: var(--kui-orange-600);
      font-weight: 700;
      font-size: 14px;
      line-height: 14px;
    }

    &-lead {
      font-weight: 400;
      font-size: 16px;
      line-height: 21px;
    }

    &-figure {
      &-image {
        object-fit: cover;
      }
    }

    &-publish-date {
      display: block;
      color: var(--kui-gray-600);
      font-family: var(--kui-font-primary);
      font-size: 12px;
      margin-bottom: 5px;
    }

    &-lead {
      font-family: var(--kui-font-primary);
      font-weight: 400;
      font-size: 16px;
      line-height: 21px;
    }

    &-video {
      position: relative;

      &:before {
        content: '';
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        background: rgba(0, 0, 0, 0.5);
      }

      .icon {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;

        @include media-breakpoint-down(md) {
          width: 24px;
          height: 28px;
        }
      }
    }
  }

  // ImgRightTitleLeadDateMeta
  // ===============================
  &.style-ImgRightTitleLeadDateMeta {
    padding-bottom: 15px;

    .article-card {
      &-title {
        line-height: 30px;
      }
    }

    & > article {
      flex-direction: row;
    }
  }

  mandiner-social-interactions {
    padding-bottom: 0;
  }

  &.style-ImgRightUpperMeta {
    padding-bottom: 15px;

    article {
      figure {
        img {
          max-width: 355px;
        }
      }
    }
  }

  &.style-ImgRightTitleLeadDateMeta,
  &.style-ImgRightUpperMeta {
    position: relative;
    border-bottom: 1px solid var(--kui-gray-100);

    @include media-breakpoint-down(sm) {
      padding-bottom: 20px;
    }

    article {
      display: flex;
      flex-direction: row;
      gap: 24px;
    }

    mandiner-social-interactions {
      border: none;
      /* Allow the interactions to use full width on mobile*/
      @include media-breakpoint-down(sm) {
        width: 100%;
        ::ng-deep {
          .container-max-width {
            max-width: initial;
          }
        }
      }
    }

    .article-card {
      &-link {
        width: 100%;
      }

      &-left {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: space-between;
        gap: 5px;
        width: 100%;
      }

      &-title {
        font-family: var(--kui-font-secondary);
        width: 100%;
      }

      &-footer {
        width: 100%;
        min-height: 15px;
      }

      &-figure {
        position: relative;
        height: fit-content;

        &-image {
          width: 100%;
          max-width: 282px;
          min-width: 282px;
          object-fit: cover;

          @include media-breakpoint-down(md) {
            width: 90px;
            min-width: 90px;
            height: 90px;
          }
        }
      }

      &-label {
        display: flex;
        align-items: center;
        position: absolute;
        bottom: 0;
        left: 0;
        background: var(--kui-red-900);
        color: var(--kui-white);
        text-transform: uppercase;
        padding: 6px 8px;

        @include media-breakpoint-up(md) {
          &-icon {
            margin-right: 8px;
          }
        }
      }

      &-img {
        &-sm {
          display: none;

          @include media-breakpoint-down(md) {
            display: block;
          }

          .article-card-figure-image {
            min-width: 70px;
            min-height: 70px;
          }
        }

        &-lg {
          display: none;

          @include media-breakpoint-up(md) {
            display: block;
            max-width: 100%;
          }
        }
      }

      &-title-container {
        display: flex;
        width: 100%;
        gap: 10px;
      }

      &-tags-container {
        display: flex;
        margin-bottom: 5px;
        align-items: center;
      }

      &-column-title {
        color: var(--kui-orange-600);
        font-weight: 700;
        font-size: 14px;
        line-height: 14px;
        margin-left: 10px;
        cursor: pointer;
      }
    }
  }

  // ImgSidedTitleLeadDateMeta
  // ===============================
  &.style-ImgSidedTitleLeadDateMeta {
    .article-card {
      &-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      &-title {
        margin-right: 10px;
        font-size: 20px;
        line-height: 26px;
        width: 60%;
      }

      &-label {
        display: flex;
        align-items: center;
        position: absolute;
        bottom: 0;
        left: 0;
        background: var(--kui-red-900);
        color: var(--kui-white);
        padding: 6px 8px;
      }

      &-figure {
        position: relative;
        height: 90px;
        width: 90px;

        &-image {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  // ImgTagTitleLeadDateMeta
  // ===============================
  &.style-ImgTagTitleLeadDateMeta {
    .article-card {
      &-label {
        display: flex;
        align-items: center;
        position: absolute;
        bottom: 0;
        left: 0;
        background: var(--kui-red-900);
        color: var(--kui-white);
        text-transform: uppercase;
        padding: 8px 10px;

        &-icon {
          margin-right: 8px;
        }
      }

      &-figure {
        position: relative;
        margin-bottom: 5px;

        &-image {
          width: 100%;
        }
      }

      &-title {
        font-family: var(--kui-font-secondary);
        font-weight: 700;
        font-size: 28px;
        line-height: 34px;
      }
    }
  }

  // ImgRightTitleLead
  // ===============================
  &.style-ImgRightTitleLead {
    position: relative;
    border-bottom: 1px solid var(--kui-gray-100);
    border-top: 1px solid var(--kui-gray-100);

    article {
      display: flex;
      flex-direction: row;
      gap: 24px;
      padding-top: 15px;
      padding-bottom: 15px;
    }

    .article-card {
      &-link {
        width: 100%;
      }

      &-left {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: space-between;
        width: 100%;
        gap: 5px;
      }

      &-title {
        font-family: var(--kui-font-secondary);
        width: 100%;
      }

      &-footer {
        width: 100%;
        min-height: 15px;
      }

      &-figure {
        height: fit-content;

        &-image {
          width: 100%;
          max-width: 282px;
          min-width: 282px;
          object-fit: cover;

          @include media-breakpoint-down(sm) {
            width: 90px;
            min-width: 90px;
            height: 90px;
          }
        }
      }

      &-label {
        color: var(--kui-orange-600);
        padding: 5px 0;
        font-weight: 700;
        font-size: 14px;
        line-height: 14px;

        &-icon {
          margin-right: 8px;
          margin-top: -5px;
        }
      }

      &-img {
        &-sm {
          display: none;

          @include media-breakpoint-down(md) {
            display: block;
          }

          .article-card-figure-image {
            min-width: 70px;
            min-height: 70px;
          }
        }

        &-lg {
          display: none;

          @include media-breakpoint-up(md) {
            display: block;
            max-width: 100%;
          }
        }
      }

      &-title-container {
        display: flex;
        width: 100%;
        gap: 10px;
      }

      &-tags-container {
        display: flex;
        margin-bottom: 5px;
        align-items: center;
      }

      &-column-title {
        color: var(--kui-orange-600);
        font-weight: 700;
        font-size: 14px;
        line-height: 14px;
        margin-left: 10px;
        cursor: pointer;
      }
    }
  }

  // ImgTitleDateMeta
  // ===============================
  &.style-ImgTitleDateMeta {
    .article-card {
      &-label {
        display: flex;
        align-items: center;
        position: absolute;
        bottom: 0;
        left: 0;
        background: var(--kui-red-900);
        color: var(--kui-white);
        text-transform: uppercase;
        padding: 8px 10px;

        &-icon {
          margin-right: 8px;
        }
      }

      &-figure {
        position: relative;
        margin-bottom: 5px;

        &-label {
          position: absolute;
          top: 0;
          left: 0;
          background: var(--kui-black);
          color: var(--kui-orange-600);
          padding: 5px 2px;
          font-weight: 800;
          font-size: 14px;
          line-height: 14px;
        }

        &-image {
          width: 100%;
        }
      }

      &-title {
        font-family: var(--kui-font-secondary);
        font-weight: 700;
        font-size: 24px;
        line-height: 30px;
      }
    }
  }

  // BlackBgImgTitleMeta
  // ===============================
  &.style-BlackBgImgTitleMeta {
    background-color: var(--kui-black);

    .article-card {
      &-figure {
        position: relative;
        margin-bottom: 5px;

        &-image {
          width: 100%;
        }
      }

      &-title {
        color: var(--kui-white);
        font-family: var(--kui-font-secondary);
        font-weight: 700;
        font-size: 24px;
        line-height: 30px;
      }
    }
  }

  // ImgTitleLeadDateMeta
  // ===============================
  &.style-ImgTitleLeadDateMeta {
    &.wide {
      margin: 0;
      width: 100%;

      h2 {
        padding: 0 15px;
        margin-bottom: 5px;
      }

      .article-card {
        &-figure {
          img {
            width: 100%;
            object-fit: cover;
          }
        }

        &-lead {
          padding: 15px;
        }
      }
    }

    .article-card {
      &-label {
        display: flex;
        align-items: center;
        position: absolute;
        bottom: 0;
        left: 0;
        background: var(--kui-red-900);
        color: var(--kui-white);
        text-transform: uppercase;
        padding: 8px 10px;

        &-icon {
          margin-right: 8px;
        }
      }

      &-figure {
        position: relative;
        margin-bottom: 5px;

        &-label {
          position: absolute;
          top: 0;
          left: 0;
          background: var(--kui-black);
          color: var(--kui-orange-600);
          padding: 5px 2px;
          font-weight: 800;
          font-size: 14px;
          line-height: 14px;
        }
      }

      &-title {
        font-family: var(--kui-font-secondary);
        font-weight: 700;
        font-size: 24px;
        line-height: 30px;
      }
    }
  }

  // ItalicTitleDateMeta
  // ===============================
  &.style-ItalicTitleDateMeta {
    .article-card {
      &-label {
        color: var(--kui-orange-600);
        padding: 5px 0;
        font-weight: 700;
        font-size: 14px;
        line-height: 14px;

        &-icon {
          margin-right: 8px;
          margin-top: -5px;
        }
      }

      &-card-title {
        font-family: var(--kui-font-secondary);
        font-style: italic;
        font-weight: 500;
        font-size: 24px;
        line-height: 30px;
      }
    }
  }

  // ItalicTitleLeadDateMeta
  // ===============================
  &.style-ItalicTitleLeadDateMeta {
    .article-card {
      &-label {
        color: var(--kui-orange-600);
        padding: 5px 0;
        font-weight: 700;
        font-size: 14px;
        line-height: 14px;

        &-icon {
          margin-right: 8px;
          margin-top: -5px;
        }
      }

      &-title {
        font-family: var(--kui-font-secondary);
        font-style: italic;
        font-weight: 500;
        font-size: 24px;
        line-height: 30px;
      }
    }
  }

  // TitleDateMeta
  // ===============================
  &.style-TitleDateMeta {
    .article-card {
      &-label {
        color: var(--kui-orange-600);
        padding: 5px 0;
        font-weight: 700;
        font-size: 14px;
        line-height: 14px;

        &-icon {
          margin-right: 8px;
          margin-top: -5px;
        }
      }

      &-card-title {
        font-family: var(--kui-font-secondary);
        font-weight: 700;
        font-size: 28px;
        line-height: 34px;
      }
    }
  }

  // TitleMeta
  // ===============================
  &.style-TitleMeta {
    margin-bottom: 5px;

    ul {
      @include unordered-list();

      li:before {
        top: calc(50% - 5px);
      }
    }

    .article-card-link {
      display: flex;
    }

    .article-card-title {
      font-family: var(--kui-font-primary);
      font-weight: 600;
      font-size: 16px;
      line-height: 20px !important; // .fs-20 lh-ja override-olja
      display: inline-block;
    }

    .seperator {
      margin-top: 5px;
      height: 1px;
      width: 80px;
      background-color: var(--kui-orange-600);
    }
  }

  // TitleLeadDateMeta, PodcastTitleLeadDateMeta
  // ===============================
  &.style-TitleLeadDateMeta,
  &.style-PodcastTitleLeadDateMeta {
    .article-card {
      &-label {
        color: var(--kui-orange-600);
        padding: 5px 0;
        font-weight: 700;
        font-size: 14px;
        line-height: 14px;

        &-icon {
          margin-right: 8px;
          margin-top: -5px;
        }
      }

      &-title {
        font-family: var(--kui-font-secondary);
        font-weight: 700;
        font-size: 28px;
        line-height: 34px;
      }

      &-subtitle {
        font-family: var(--kui-font-primary);
        font-weight: 700;
        font-size: 14px;
        line-height: 14px;
        padding-top: 5px;

        display: inline-flex;
        align-items: center;

        color: var(--kui-orange-600);
      }
    }
  }

  // UpperTitleLeadDateMeta
  // ===============================
  &.style-UpperTitleLeadDateMeta {
    .article-card-title {
      font-family: var(--kui-font-secondary);
      font-weight: 700;
      font-size: 28px;
      line-height: 34px;
      text-transform: uppercase;
    }
  }

  // OnlyTitleWithBullet
  // ===============================
  &.style-OnlyTitleWithBullet {
    .article-card {
      &-list {
        &-item {
          font-family: var(--kui-font-primary);
          font-weight: 600;
          font-size: 16px;
          line-height: 20px;
          position: relative;
          margin-bottom: 15px;

          &::before {
            content: '';
            width: 8px;
            height: 8px;
            background: var(--kui-orange-600);
            border-radius: 50%;
            position: absolute;
            top: 8px;
            left: -15px;
          }

          &::after {
            content: '';
            width: 80px;
            height: 1px;
            background: var(--kui-orange-600);
            position: absolute;
            bottom: -10px;
            left: -15px;
          }
        }
      }
    }
  }

  // ExternalRecommendation
  // ===============================
  &.style-ExternalRecommendation {
    .article-card {
      &-figure {
        position: relative;
        margin-bottom: 5px;

        img {
          width: 100%;
          aspect-ratio: 16/9;
          object-fit: cover;
          object-position: top center;
        }
      }

      &-title {
        font-family: var(--kui-font-primary);
        font-weight: 600;
        font-size: 20px;
        line-height: 26px;
      }

      &-source {
        color: var(--kui-orange-600);
        font-weight: 700;
        font-size: 12px;
        line-height: 14px;
        text-transform: uppercase;
      }
    }
  }

  // BreakingNewsArticleUp
  // ===============================
  &.style-BreakingNewsArticleUp {
    section {
      @extend %fullWidthScreen;
    }

    .image-container {
      position: relative;
    }

    .article-card {
      &-image {
        width: 100%;
        max-height: 810px; // From figma.
        object-fit: cover;
      }

      &-info-block {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        padding: 20px 0;
        display: flex;
        justify-content: center;
        background: rgb(0, 0, 0, 0.6);
        @include media-breakpoint-down(md) {
          display: none;
        }
      }

      &-heading {
        max-width: 792px;
        justify-content: center;
        align-items: center;
        display: flex;
        flex-direction: column;
      }

      &-title {
        font-family: var(--kui-font-secondary);
        font-weight: 700;
        font-size: 32px;
        line-height: 38px;
        color: var(--kui-white);
        text-align: center;
        margin-bottom: 5px;
      }

      &-lead {
        font-weight: 400;
        font-size: 18px;
        line-height: 21px;
        color: var(--kui-white);
        text-align: center;
      }

      &-source {
        color: var(--kui-orange-600);
        font-weight: 700;
        font-size: 12px;
        line-height: 14px;
        text-transform: uppercase;
      }
    }

    .mobile-box {
      display: none;
      @include media-breakpoint-down(md) {
        display: flex;
        color: var(--kui-white);
        background-color: var(--kui-black);

        .heading {
          margin: 20px;
        }

        .title {
          color: var(--kui-white);
          font-family: var(--kui-font-secondary);
          font-weight: 700;
          font-size: 28px;
          line-height: 34px;
          text-align: center;
          margin-bottom: 5px;
        }

        .lead {
          color: var(--kui-white);
          font-family: var(--kui-font-primary);
          font-weight: 400;
          font-size: 16px;
          line-height: 21px;
          text-align: center;
        }
      }
    }
  }

  // BreakingNewsArticleDown
  // ===============================
  &.style-BreakingNewsArticleDown {
    section {
      @extend %fullWidthScreen;
    }

    .image-container {
      position: relative;
    }

    .article-card {
      &-image-container {
        position: relative;
      }

      &-image {
        width: 100%;
        max-height: 810px; // From figma.
        object-fit: cover;
      }

      &-info-block {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        padding: 20px 0;
        display: flex;
        justify-content: center;
        background: rgb(0, 0, 0, 0.6);
        @include media-breakpoint-down(md) {
          display: none;
        }
      }

      &-heading {
        max-width: 792px;
        justify-content: center;
        align-items: center;
        display: flex;
        flex-direction: column;
      }

      &-title {
        font-family: var(--kui-font-secondary);
        font-weight: 700;
        font-size: 32px;
        line-height: 38px;
        color: var(--kui-white);
        text-align: center;
        margin-bottom: 5px;
      }

      &-lead {
        font-weight: 400;
        font-size: 18px;
        line-height: 21px;
        color: var(--kui-white);
        text-align: center;
      }

      &-source {
        color: var(--kui-orange-600);
        font-weight: 700;
        font-size: 12px;
        line-height: 14px;
        text-transform: uppercase;
      }
    }

    .mobile-box {
      display: none;
      @include media-breakpoint-down(md) {
        display: flex;
        color: var(--kui-white);
        background-color: var(--kui-black);

        .heading {
          margin: 20px;
        }

        .title {
          color: var(--kui-white);
          font-family: var(--kui-font-secondary);
          font-weight: 700;
          font-size: 28px;
          line-height: 34px;
          text-align: center;
          margin-bottom: 5px;
        }

        .lead {
          color: var(--kui-white);
          font-family: var(--kui-font-primary);
          font-weight: 400;
          font-size: 16px;
          line-height: 21px;
          text-align: center;
        }
      }
    }
  }

  &.style-TitleDateWithoutMeta {
    .article-card {
      &-twenty-four {
        display: flex;
        margin-bottom: 5px;
        align-items: center;
        gap: 10px;
      }

      &-title {
        font-size: 16px;
        line-height: 20px;
        font-weight: 600;
        font-family: var(--kui-font-primary);
      }

      &-divider {
        width: 1px;
        min-width: 1px;
        background: var(--kui-orange-600);
        height: 20px;
      }

      &-publish-date {
        margin-bottom: 0;
      }
    }
  }

  // ImgSixteenNineTitleLeadMeta
  // ===============================
  &.style-ImgSixteenNineTitleLeadMeta {
    .article-card {
      &-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      &-title {
        margin-right: 10px;
        font-size: 20px;
        line-height: 26px;
        width: 60%;
      }

      &-label {
        display: flex;
        align-items: center;
        position: absolute;
        bottom: 0;
        left: 0;
        background: var(--kui-red-900);
        color: var(--kui-white);
        padding: 6px 8px;
      }

      &-figure {
        position: relative;
        height: 90px;
        width: 160px;

        &-image {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  // ArticleDetailStyle
  // ===============================
  &.style-ArticleDetailStyle {
    position: relative;

    article {
      display: flex;
      flex-direction: row;
      gap: 24px;
    }

    .article-card {
      &-lead {
        line-height: 22px;
      }

      &-link {
        width: 100%;
      }

      &-left {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
        width: 100%;
        gap: 5px;

        @include media-breakpoint-down(sm) {
          gap: 12px;
        }
      }

      &-title {
        font-family: var(--kui-font-primary);
        width: 100%;
      }

      &-footer {
        width: 100%;
        min-height: 15px;
      }

      &-figure {
        height: fit-content;

        &-image {
          width: 100%;
          max-width: 160px;
          min-width: 160px;
          object-fit: cover;

          @include media-breakpoint-down(md) {
            width: 90px;
            min-width: 90px;
            height: 90px;
          }
        }
      }

      &-label {
        color: var(--kui-orange-600);
        padding: 5px 0;
        font-weight: 700;
        font-size: 14px;
        line-height: 14px;

        &-icon {
          margin-right: 8px;
          margin-top: -5px;
        }
      }

      &-img {
        &-sm {
          display: none;

          @include media-breakpoint-down(md) {
            display: block;
          }

          .article-card-figure-image {
            min-width: 70px;
            min-height: 70px;
          }
        }

        &-lg {
          display: none;

          @include media-breakpoint-up(md) {
            display: block;
            max-width: 100%;
          }
        }
      }

      &-title-container {
        display: flex;
        width: 100%;
        gap: 10px;
      }

      &-tags-container {
        display: flex;
        margin-bottom: 5px;
        align-items: center;
      }

      &-column-title {
        color: var(--kui-orange-600);
        font-weight: 700;
        font-size: 14px;
        line-height: 14px;
        margin-left: 10px;
        cursor: pointer;
      }
    }
  }
}

article {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  gap: 5px;
}

%fullWidthScreen {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}
