import { Async<PERSON>ip<PERSON>, <PERSON><PERSON>UMENT, Ng<PERSON>lass, NgFor<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ng<PERSON><PERSON>, NgSwitchCase, NgTemplateOutlet } from '@angular/common';
import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, inject, Inject, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router, RouterLink } from '@angular/router';
import { FormatDatePipe, IMetaData, SchemaOrgService, SeoService, StorageService, UtilService } from '@trendency/kesma-core';
import {
  AnalyticsService,
  Article,
  ArticleAuthor,
  ArticleBodyType,
  ArticleCard,
  ArticleCardWithSocial,
  ArticleFileLinkDirective,
  ArticleRouteParams,
  ArticleSearchResult,
  ArticleVideoComponent,
  BackendArticleSearchResult,
  BasicDossier,
  BreadcrumbItem,
  buildArticleUrl,
  buildColumnUrl,
  ElectionsBoxComponent,
  ElectionsBoxStyle,
  FocusPointDirective,
  FollowStatus,
  GalleryData,
  GalleryElementData,
  getStructuredDataForArticle,
  mapBackendArticleDataToArticleCardWithHideThumbnail,
  MinuteToMinuteBlock,
  MinuteToMinuteState,
  NativtereloComponent,
  NEWSLETTER_COMPONENT_TYPE,
  OlimpiaNavigatorComponent,
  OlimpicPortalEnum,
  PortalConfigSetting,
  Tag,
  VoteDataWithAnswer,
  VoteService,
} from '@trendency/kesma-ui';
import { flatten } from 'lodash-es';
import { FormatPipeModule } from 'ngx-date-fns';
import { forkJoin, Observable, Subject, Subscription } from 'rxjs';
import { finalize, switchMap, takeUntil, tap } from 'rxjs/operators';
import { StrossleAdvertComponent } from 'src/app/shared/components/strossle-advert/strossle-advert.component';
import { defaultMetaInfo } from 'src/app/shared/constants/meta.consts';
import { AdvertService } from 'src/app/shared/services/advert.service';
import { AuthService } from 'src/app/shared/services/auth.service';
import { OlimpiaService } from 'src/app/shared/services/olimpia.service';
import { SecureApiService } from 'src/app/shared/services/secure-api.service';
import { environment } from 'src/environments/environment';
import {
  ArticleCardType,
  ArticleService,
  ElectionsService,
  ExternalRecommendationsComponent,
  FollowHelperService,
  GalleryService,
  LikeButtons,
  makeBreadcrumbSchema,
  ManAdultComponent,
  ManArticleCardComponent,
  ManAuthorInfoComponent,
  ManBlockTitleSmallComponent,
  ManBreadcrumbComponent,
  MandinerArticleResolverData,
  MandinerVotingStyle,
  ManGalleryCardComponent,
  ManNewsletterDiverterCardComponent,
  ManOpinionCardComponent,
  ManOpinionCardType,
  ManQuizComponent,
  ManSocialInteractionsComponent,
  ManSubscriptionBlockComponent,
  ManTrendingTopicsComponent,
  ManVotingComponent,
  ManWysiwygBoxComponent,
  NewsletterDiverterCardType,
  PersonalizedRecommendationService,
  PortalConfigService,
  setArticleSourceHost,
  SocialInteractionEvent,
  SocialInteractionEventType,
} from '../../shared';
import { ArticleDossierRecommenderComponent } from '../../shared/components/article-dossier-recommender/article-dossier-recommender.component';
import { CalendarComponent } from '../../shared/components/calendar/calendar.component';
import { FoundationRecommendationComponent } from '../../shared/components/foundation-recommendation/foundation-recommendation.component';
import { CalendarType } from '../../shared/definitions/calendar.definitions';
import { CommentSectionComponent } from '../comment-section/components/comment-section/comment-section/comment-section.component';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { searchResultToArticleCard } from '../tags-page/tags-page.utils';

@Component({
  selector: 'app-article-page',
  templateUrl: './article-page.component.html',
  styleUrls: ['./article-page.component.scss'],
  providers: [FormatDatePipe],
  imports: [
    NgIf,
    ManAdultComponent,
    ManBreadcrumbComponent,
    ManTrendingTopicsComponent,
    NgClass,
    NgTemplateOutlet,
    FocusPointDirective,
    ManAuthorInfoComponent,
    AsyncPipe,
    OlimpiaNavigatorComponent,
    ManSubscriptionBlockComponent,
    ManArticleCardComponent,
    NgForOf,
    CalendarComponent,
    FormatDatePipe,
    FoundationRecommendationComponent,
    ArticleDossierRecommenderComponent,
    ManNewsletterDiverterCardComponent,
    CommentSectionComponent,
    NativtereloComponent,
    ElectionsBoxComponent,
    SidebarComponent,
    NgSwitch,
    NgSwitchCase,
    ManWysiwygBoxComponent,
    ArticleFileLinkDirective,
    ManVotingComponent,
    ManQuizComponent,
    ArticleVideoComponent,
    ManGalleryCardComponent,
    RouterLink,
    ManOpinionCardComponent,
    ManBlockTitleSmallComponent,
    FormatPipeModule,
    ManSocialInteractionsComponent,
    StrossleAdvertComponent,
    ExternalRecommendationsComponent,
  ],
})
export class ArticlePageComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('dataTrigger', { static: false }) readonly dataTrigger: ElementRef<HTMLDivElement>;
  @ViewChild('commentSection', { static: false }) readonly commentSection: CommentSectionComponent;

  private readonly advertService = inject(AdvertService);

  readonly CalendarType = CalendarType;
  readonly ArticleCardType = ArticleCardType;
  readonly ArticleBodyType = ArticleBodyType;

  readonly NewsletterDiverterCardType = NewsletterDiverterCardType;
  readonly MinuteToMinuteState = MinuteToMinuteState;
  readonly ManOpinionCardType = ManOpinionCardType;

  article: Article;
  articleSlug = '';
  categorySlug = '';
  relatedArticles: ArticleCard[] = [];
  isUserAdultChoice: boolean;
  metaData: IMetaData;
  galleries: Record<string, GalleryData> = {};
  dossier?: BasicDossier;
  fbShareUrl: string;
  twitterShareUrl: string;
  baseUrl: string;
  mailToLink: string;
  routerEventSub: Subscription;
  moreArticlesByAuthor: ArticleCard[];
  authorData?: ArticleAuthor;
  breadcrumbItems?: BreadcrumbItem[];
  votingStyle = MandinerVotingStyle;
  articleSource: string;
  isInnerAuthor = false;
  articleLink: string[] = [];
  socialInteractionData: LikeButtons;
  isLoggedIn = false;
  isBookmarked = false;
  user$ = this.authService.currentUserSubject.asObservable();
  userSubscription: Subscription = this.user$.subscribe();
  currentUrl: string;
  isAuthorFollowed$: Observable<FollowStatus> = (
    this.route.data as Observable<{
      articlePageData: MandinerArticleResolverData;
    }>
  ).pipe(switchMap(() => this.followHelperService.getAuthorFollowedStatus(this.authorData?.slug ?? '')));
  isAuthorFollowButtonLoading = false;
  foundationTagSlug?: string;
  foundationTagTitle?: string;
  ElectionsBoxStyle = ElectionsBoxStyle;
  OlimpicPortalEnum = OlimpicPortalEnum;
  tereloUrl: string =
    'https://terelo.mediaworks.hu/nativterelo/nativterelo.html?utmSource=mandiner.hu' + '&traffickingPlatforms=Mandiner%20Nat%C3%ADv' + '&domain=Mandiner';
  private url: string;
  private cannonicalUrl: string;
  private readonly unsubscribe$: Subject<boolean> = new Subject();
  private loading: boolean;
  private isOlimpia2024: boolean;

  readonly voteCache = this.voteService.voteCache;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly seo: SeoService,
    private readonly utilsService: UtilService,
    private readonly storage: StorageService,
    private readonly formatDate: FormatDatePipe,
    private readonly galleryService: GalleryService,
    private readonly schemaService: SchemaOrgService,
    private readonly analyticsService: AnalyticsService,
    public readonly voteService: VoteService,
    private readonly changeDetectorRef: ChangeDetectorRef,
    private readonly secureApiService: SecureApiService,
    private readonly authService: AuthService,
    private readonly articleService: ArticleService,
    private readonly storageService: StorageService,
    private readonly portalConfigService: PortalConfigService,
    private readonly followHelperService: FollowHelperService,
    private readonly personalizedRecommendationService: PersonalizedRecommendationService,
    public readonly electionsService: ElectionsService,
    public readonly olimpiaService: OlimpiaService,
    @Inject(DOCUMENT) private readonly document: Document
  ) {}

  ngOnInit(): void {
    (
      this.route.data as Observable<{
        articlePageData: MandinerArticleResolverData;
      }>
    )
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(
        ({
          articlePageData: {
            article: { data: article, articles },
            relatedArticles,
            articleSlug,
            categorySlug,
            url,
            foundationTagSlug,
            foundationTagTitle,
          },
        }: {
          articlePageData: MandinerArticleResolverData;
        }) => {
          if (!article) {
            article = {} as Article; // quick'n'dirty way to mitigate missing body caused nullpointer exceptions
          }
          this.foundationTagSlug = foundationTagSlug;
          this.foundationTagTitle = foundationTagTitle;

          const body = article.body;

          this.article = {
            ...article,
            slug: articleSlug,
            body: this.articleService.prepareArticleBody(body),
            minuteToMinuteBlocks: article.minuteToMinuteBlocks?.map((block: MinuteToMinuteBlock) => ({
              ...block,
              body: this.articleService.prepareArticleBody(block?.body),
            })),
            excerpt: article?.lead || article?.excerpt,
            articleSource: this.transformArticleSource(article.articleSource || ''),
          };

          const enabledExternalSlug = this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_EXTERNAL_PUBLIC_AUTHOR_SLUG);

          this.isInnerAuthor = article?.publicAuthorIsInner || false;
          this.articleSlug = articleSlug;
          this.articleLink = this.article ? buildArticleUrl(this.article) : [];
          this.setBreadcrumbItems(this.article);
          this.authorData = {
            name: this.article.publicAuthor ?? 'Mandíner',
            description: this.article.publicAuthorDescription,
            title: this.article.publicAuthorTitle,
            avatar: this.article.avatar,
            articleMedium: this.article.articleMedium,
            slug: enabledExternalSlug ? this.article.publicAuthorSlug : this.article.publicAuthor,
          };

          if (article?.isOpinion) {
            this.moreArticlesByAuthor = (articles?.data as unknown as BackendArticleSearchResult[])
              ?.filter((res: BackendArticleSearchResult) => res.id !== this.article.id)
              ?.map(mapBackendArticleDataToArticleCardWithHideThumbnail);
          }

          this.categorySlug = categorySlug ?? this.article?.primaryColumn?.slug;

          this.url = url ?? '';
          const articleYear = this.article?.publishDate?.getFullYear().toString();
          const articleMonth = this.article?.publishDate?.getMonth() ? (this.article?.publishDate?.getMonth() + 1).toString().padStart(2, '0') : undefined;
          const generatedCanonicalUrl =
            articleYear && articleMonth && !foundationTagSlug
              ? `${this.seo.hostUrl}/${this.article.primaryColumn.slug}/${articleYear}/${articleMonth}/${this.article.slug}`
              : `${this.seo.hostUrl}/${this.url}`;
          this.cannonicalUrl = this.article.seo?.seoCanonicalUrl || this.article?.canonicalUrl || generatedCanonicalUrl;
          this.dossier = this.article?.dossier;
          this.socialInteractionData = {
            like: this.article.likeCount,
            dislike: this.article.dislikeCount,
            comment: this.article.commentCount,
            readingTime: this.article.readingTime,
            areCommentsHidden: (this.article as ArticleCardWithSocial).isCommentsDisabled,
            areReactionsHidden: (this.article as ArticleCardWithSocial).isLikesAndDislikesDisabled,
          };
          this.articleSource = setArticleSourceHost(this.article.articleSource);

          // ??? const withoutAds = this.article?.withoutAds;

          this.isUserAdultChoice = (this.storage.getSessionStorageData('isAdultChoice', false) ?? false) && this.article.isAdultsOnly;

          // Should make it empty because if the user opens another article, contact will join those relatedArticles into the existing array
          this.relatedArticles = [];
          this.relatedArticles = this.relatedArticles.concat(
            relatedArticles.data?.map((searchResult: ArticleSearchResult) => {
              const social = searchResult as any as ArticleCardWithSocial;
              return {
                ...searchResultToArticleCard(searchResult),
                likeCount: social.likeCount,
                dislikeCount: social.dislikeCount,
                commentCount: social.commentCount,
                isCommentsDisabled: social.isCommentsDisabled,
                isLikesAndDislikesDisabled: social.isLikesAndDislikesDisabled,
              };
            })
          );

          this.onSetIsOlimpia(this.article.tags);
          this.seo.updateCanonicalUrl(this.cannonicalUrl ?? '', { addHostUrl: false, skipSeoMetaCheck: true });
          // this.loadEmbedMediaContent();
          this.loadEmbeddedGalleries();
          this.checkIsLoggedIn();
          if (this.article) {
            this.schemaService.insertSchema(getStructuredDataForArticle(this.article, this.seo.currentUrl, environment?.siteUrl ?? ''));

            if (this.breadcrumbItems) {
              const breadcrumbSchema = makeBreadcrumbSchema(this.breadcrumbItems);
              this.schemaService.insertSchema(breadcrumbSchema);
            }
          }
          this.setMetaData();
          this.currentUrl = this.router.url;
          this.setupShareLinks();

          this.voteService.initArticleVotes(this.article);

          this.advertService.handleAdDisabling(this.article.withoutAds);

          setTimeout(() => {
            this.analyticsService.sendPageView({
              pageCategory: this.categorySlug,
              customDim2: this.article?.topicLevel1,
              customDim1: this.article?.aniCode,
              title: this.article.title,
              articleSource: this.article.articleSource ? this.article.articleSource : 'no source',
              publishDate: this.formatDate.transform(this.article.publishDate as Date, 'dateTime'),
              lastUpdatedDate: this.formatDate.transform((this.article.lastUpdated ? this.article.lastUpdated : this.article.publishDate) as Date, 'dateTime'),
            });
          }, 0);

          this.changeDetectorRef.detectChanges();
        }
      );
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
    this.schemaService.removeStructuredData();
    this.olimpiaService.setIsOlimpiaMainOrArticlePage(false);
    this.userSubscription?.unsubscribe();

    if (this.article?.withoutAds) {
      this.advertService.enableAds();
    }
  }

  ngAfterViewInit(): void {
    if (this.utilsService.isBrowser()) {
      setTimeout(() => {
        this.route.data.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {
          if ('IntersectionObserver' in window) {
            this.observeArticleEnd();
          }
        });
      }, 1000);
    }
  }

  onSubscribeClicked(): void {
    this.analyticsService.newsLetterSubscriptionClicked(NEWSLETTER_COMPONENT_TYPE.ARTICLE_END);
    window.open('/hirlevel-feliratkozas', '_blank');
  }

  onIsUserAdultChoose(isUserAdult: boolean): void {
    this.isUserAdultChoice = isUserAdult;
    // this.initDataLayer();
  }

  setupShareLinks(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    this.mailToLink = 'mailto:?subject=' + this.metaData?.ogTitle + '&body=' + this.metaData?.ogTitle + ': ' + this.seo.currentUrl;
    this.baseUrl = this.seo.hostUrl;
    this.fbShareUrl = `https://www.facebook.com/sharer/sharer.php?u=${this.baseUrl}${this.currentUrl}`;
    this.twitterShareUrl = `https://twitter.com/intent/tweet?url=${this.baseUrl}${this.currentUrl}`;

    this.routerEventSub = this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe((event: any) => {
      if (event instanceof NavigationEnd) {
        this.fbShareUrl = `https://www.facebook.com/sharer/sharer.php?u=${this.baseUrl}${event.url}`;
        this.twitterShareUrl = `https://twitter.com/intent/tweet?url=${this.baseUrl}${event.url}`;
        this.mailToLink = 'mailto:?subject=' + this.metaData.ogTitle + '&body=' + this.metaData.ogTitle + ': ' + this.seo.currentUrl;
      }
    });
  }

  onVotingSubmit($event: string, voteData: VoteDataWithAnswer): void {
    this.voteService.onVotingSubmit($event, voteData).subscribe(() => this.changeDetectorRef.detectChanges());
  }

  refreshArticle(): void {
    if (this.utilsService.isBrowser()) {
      this.document.defaultView?.location.reload();
    }
  }

  clickOnFollow(): void {
    if (this.article.isOpinion) {
      window.open(this.article.articleSource, '_blank');
    }
  }

  onSetIsOlimpia(tags: Tag[]): void {
    const tagSlugs = tags?.map(({ slug }) => slug);
    this.isOlimpia2024 = tagSlugs?.includes('olimpia_2024');
    if (!this.isOlimpia2024) return;

    this.olimpiaService.setIsOlimpiaMainOrArticlePage(this.isOlimpia2024);
  }

  transformArticleSource(source: string): string {
    if (!source) {
      return '';
    }

    const whiteList = ['www.', 'http://', 'https://'];

    if (!whiteList.some((item) => source?.startsWith(item))) return '';
    if (source?.startsWith('www.')) source = 'https://' + source.slice(4);

    return source;
  }

  handleBookmartClicked(method: string): void {
    if (!this.article.id) {
      return;
    }

    if (!this.isLoggedIn) {
      this.storageService.setLocalStorageData('loginRedirectUrl', this.currentUrl);
      this.router.navigate(['/bejelentkezes']).then();
    }
    if (this.isLoggedIn && method === 'save') {
      this.secureApiService.postSavedArticles(this.article.id).subscribe(() => {
        this.isBookmarked = true;
        this.changeDetectorRef.markForCheck();
      });
    }

    if (this.isLoggedIn && method === 'delete') {
      this.secureApiService.deleteSavedArticles(this.article.id).subscribe(() => {
        this.isBookmarked = false;
        this.changeDetectorRef.markForCheck();
      });
    }
  }

  handleReactionClicked(liked: boolean): void {
    if (!this.isLoggedIn) {
      this.storageService.setLocalStorageData('loginRedirectUrl', this.currentUrl);
      this.router.navigate(['/bejelentkezes']).then();
      return;
    }
    if (this.loading) {
      return;
    }

    this.loading = true;
    this.secureApiService
      .getJudgement(this.article.id)
      .pipe(
        switchMap((res) => {
          if (!liked) {
            if (res.data.dislike) {
              this.handleDislikes(true);
              return this.secureApiService.clearJudgement(this.article.id);
            } else {
              this.handleDislikes(false);
              if (res.data.like) {
                this.handleLikes(true);
              }
              return this.secureApiService.postReaction(this.article.id, false);
            }
          } else {
            if (res.data.like) {
              this.handleLikes(true);
              return this.secureApiService.clearJudgement(this.article.id);
            } else {
              this.handleLikes(false);
              if (res.data.dislike) {
                this.handleDislikes(true);
              }
              return this.secureApiService.postReaction(this.article.id, true);
            }
          }
        }),
        tap(() => this.changeDetectorRef.detectChanges()),
        takeUntil(this.unsubscribe$),
        finalize(() => {
          setTimeout(() => {
            // A timeout is needed so that the backend can save the reaction
            this.loading = false;
          }, 1000);
        })
      )
      .subscribe();
  }

  handleLikes(remove: boolean): void {
    if (this.socialInteractionData.like !== undefined) {
      this.socialInteractionData = {
        ...this.socialInteractionData,
        like: this.socialInteractionData.like + (remove ? -1 : 1),
      };
    }
  }

  handleDislikes(remove: boolean): void {
    if (this.socialInteractionData.dislike !== undefined) {
      this.socialInteractionData = {
        ...this.socialInteractionData,
        dislike: this.socialInteractionData.dislike + (remove ? -1 : 1),
      };
    }
  }

  scrollToComments(): void {
    this.commentSection.scrollToTarget();
  }

  handleCommentSubmitted(): void {
    this.socialInteractionData = {
      ...this.socialInteractionData,
      comment: this.socialInteractionData.comment ? this.socialInteractionData.comment + 1 : 1,
    };
  }

  // Any from kesma-ui ArticleBodyDetails
  doubleArticleRecommendation(articleBodyDetails: any): ArticleCard {
    if (articleBodyDetails.thumbnailUrl) {
      articleBodyDetails.thumbnail = { url: articleBodyDetails?.thumbnailUrl };
    }
    return {
      ...articleBodyDetails,
      thumbnailFocusedImages: articleBodyDetails?.thumbnailUrlFocusedImages,
      likeCount: articleBodyDetails?.dbcache?.likeCount ?? 0,
      dislikeCount: articleBodyDetails?.dbcache?.dislikeCount ?? 0,
      commentCount: articleBodyDetails?.dbcache?.commentsCount ?? 0,
    };
  }

  // Any from kesma-ui ArticleBodyDetails
  doubleArticleRecommendations(arr: any[]): any[] {
    return arr.filter((elem: any) => elem?.value?.id);
  }

  onSocialInteraction($event: SocialInteractionEvent): void {
    this.analyticsService.sendSocialInteraction({
      clickLink: $event.url ?? 'no data',
      clickText: $event.linkText ?? 'no data',
    });

    if ($event.event === SocialInteractionEventType.FacebookShare) {
      this.analyticsService.sendFacebookShare({
        clickLink: $event.url ?? 'no data',
        title: $event.title ?? 'no data',
        publishDate: $event.publishDate,
      });
    }
  }

  handleAuthorFollow(authorSlug: string): void {
    this.isAuthorFollowButtonLoading = true;
    this.followHelperService.handleAuthorFollow(authorSlug).subscribe(() => {
      this.isAuthorFollowButtonLoading = false;
      this.changeDetectorRef.detectChanges();
    });
  }

  private setBreadcrumbItems(article: Article): void {
    const isOpinion = article.isOpinion;
    const columnParent = article.primaryColumn?.parent;
    this.breadcrumbItems = [
      {
        label: isOpinion ? 'Vélemények' : (article.primaryColumn?.title ?? article.columnTitle),
        url: isOpinion ? ['/', 'velemeny'] : buildColumnUrl(article),
      },
    ];
    if (!isOpinion && columnParent) {
      this.breadcrumbItems.unshift({
        label: columnParent.title,
        url: buildColumnUrl({ columnSlug: columnParent.slug } as any),
      });
    }
  }

  private setMetaData(): void {
    const { thumbnail, publicAuthor, publishDate, alternativeTitle, metaThumbnail } = this.article || {};
    if (!this.article) {
      return;
    }

    const title = this.article.seo?.seoTitle || this.article.title;
    const ogTitle = this.article.title;
    const finalTitle = alternativeTitle ?? title;
    const finalOgTitle = alternativeTitle ?? ogTitle;
    const opinionOgTitle = `${this.article.publicAuthor}: ${finalOgTitle}`.trim();
    this.metaData = {
      ...defaultMetaInfo,
      title: finalTitle,
      description: this.article.seo?.seoDescription || this.article.excerpt || this.article.lead || defaultMetaInfo.description,
      robots: this.article.seo?.seoRobotsMeta || 'index, follow, max-image-preview:large',
      ogTitle: this.article.isOpinion ? opinionOgTitle : finalOgTitle,
      ogImage: metaThumbnail || thumbnail,
      ogType: 'article',
      articleAuthor: publicAuthor,
      articlePublishedTime: publishDate?.toISOString(),
    };

    this.seo.setMetaData(this.metaData);
  }

  private loadEmbeddedGalleries(): void {
    let bodyElements = (this.article?.body as any) ?? [];
    if (this.article?.minuteToMinute !== MinuteToMinuteState.NOT) {
      bodyElements = bodyElements.concat(flatten((this.article?.minuteToMinuteBlocks?.map((blocks: MinuteToMinuteBlock) => blocks?.body) as any) ?? []));
    }

    const gallerySubs = ((bodyElements ?? []) as GalleryElementData[])
      .filter(({ type }) => type === ArticleBodyType.Gallery)
      .filter((bodyElem) => !!bodyElem.details[0].value)
      .map((bodyElem: GalleryElementData) => this.galleryService.getGalleryDetails(bodyElem.details[0].value.slug));

    forkJoin(gallerySubs)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((galleries) => {
        galleries.forEach((gallery) => {
          const galleryData: GalleryData = {
            ...gallery,
            highlightedImageUrl: gallery.highlightedImage.url,
          } as any as GalleryData;
          this.galleries[gallery.id] = galleryData;
        });
      });
  }

  private observeArticleEnd(): void {
    if (!this.dataTrigger?.nativeElement) {
      return;
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(({ isIntersecting }) => {
        if (isIntersecting) {
          this.sendEcommerceEvent();
          observer.unobserve(this.dataTrigger.nativeElement);
        }
      });
    });
    observer.observe(this.dataTrigger.nativeElement);
  }

  private sendEcommerceEvent(): void {
    const routeParams: ArticleRouteParams = this.route.snapshot.params as ArticleRouteParams;
    this.analyticsService.sendEcommerceEvent({
      id: `T${this.article.id}`,
      title: this.article.title,
      articleSlug: routeParams.articleSlug ? routeParams.articleSlug : 'cikk-elonezet',
      category: this.article.columnTitle ?? '',
      articleSource: this.article.articleSource ? this.article.articleSource : 'no source',
      publishDate: this.formatDate.transform(this.article.publishDate as Date, 'dateTime'),
      lastUpdatedDate: this.formatDate.transform((this.article.lastUpdated ? this.article.lastUpdated : this.article.publishDate) as Date, 'dateTime'),
    });
  }

  private checkIsLoggedIn(): void {
    if (this.utilsService.isBrowser()) {
      this.authService.isAuthenticated().subscribe((res: boolean) => {
        if (res) {
          this.isLoggedIn = res;
          this.saveReadingHistory();
          this.getSavedArticle();
        }
        return res;
      });
    }
  }

  private saveReadingHistory(): void {
    this.secureApiService.postReadHistory(this.article.id).subscribe();
  }

  private getSavedArticle(): void {
    this.secureApiService.getSavedArticle(this.article.id).subscribe((res: any) => {
      this.isBookmarked = res.data.saved;
      this.changeDetectorRef.markForCheck();
    });
  }
}
